"""
Patient model for CMOST simulation.
"""
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple, Any
import numpy as np
from datetime import datetime

from .polyp import Polyp
from .cancer import Cancer


@dataclass
class Patient:
    """Represents a patient in the colorectal cancer simulation."""
    
    id: int
    age: int
    gender: str  # 'M' or 'F'
    risk_factors: Dict[str, float] = field(default_factory=dict)
    polyps: List[Polyp] = field(default_factory=list)
    cancers: List[Cancer] = field(default_factory=list)
    death_year: Optional[int] = None
    death_cause: Optional[str] = None
    
    # Screening history
    screening_history: List[Dict[str, Any]] = field(default_factory=list)

    # Treatment history
    treatment_history: List[Dict[str, Any]] = field(default_factory=list)
    
    def __post_init__(self):
        """Initialize derived attributes."""
        self.individual_risk = self._calculate_individual_risk()
        self.birth_year = datetime.now().year - self.age
        self.natural_death_year = None  # Will be set based on life tables
    
    def _calculate_individual_risk(self) -> float:
        """Calculate individual risk based on risk factors.
        
        Returns:
            float: Risk multiplier where 1.0 is average risk
        """
        base_risk = 1.0
        for factor, value in self.risk_factors.items():
            base_risk *= value
        return base_risk
    
    def add_polyp(self, location: int, size: float = 0.1) -> Polyp:
        """Add a new polyp to the patient.
        
        Args:
            location: Anatomical location (1-6 representing different colon segments)
            size: Initial size in cm
            
        Returns:
            Polyp: The newly created polyp
        """
        polyp = Polyp(
            id=len(self.polyps) + 1,
            location=location,
            size=size,
            stage=1,
            patient_id=self.id,
            patient_gender=self.gender
        )
        self.polyps.append(polyp)
        return polyp
    
    def progress(self, years: int, progression_model) -> None:
        """Progress the patient's condition by a number of years.
        
        Args:
            years: Number of years to progress
            progression_model: Model that defines progression rates
        """
        # Age the patient
        self.age += years
        
        # Progress each polyp
        for polyp in self.polyps:
            polyp.progress(years, progression_model, self.individual_risk)
            
            # Check if polyp progressed to cancer
            if polyp.stage > 6 and not any(c.source_polyp_id == polyp.id for c in self.cancers):
                cancer = Cancer.from_polyp(polyp)
                self.cancers.append(cancer)
        
        # Progress each cancer
        for cancer in self.cancers:
            cancer.progress(years)
    
    def add_screening(self, year: int, test_type: str, findings: Dict[str, Any]) -> None:
        """Record a screening test.
        
        Args:
            year: Year when screening occurred
            test_type: Type of screening (e.g., 'colonoscopy', 'FOBT', 'sigmoidoscopy')
            findings: Dict containing test results
        """
        self.screening_history.append({
            'year': year,
            'test_type': test_type,
            'findings': findings
        })
    
    def add_treatment(self, year: int, treatment_type: str, details: Dict[str, Any]) -> None:
        """Record a treatment.
        
        Args:
            year: Year when treatment occurred
            treatment_type: Type of treatment (e.g., 'polypectomy', 'surgery', 'chemotherapy')
            details: Dict containing treatment details
        """
        self.treatment_history.append({
            'year': year,
            'treatment_type': treatment_type,
            'details': details
        })
    
    def remove_polyp(self, polyp_id: int) -> bool:
        """Remove a polyp (e.g., after polypectomy).
        
        Args:
            polyp_id: ID of the polyp to remove
            
        Returns:
            bool: True if polyp was found and removed, False otherwise
        """
        for i, polyp in enumerate(self.polyps):
            if polyp.id == polyp_id:
                self.polyps.pop(i)
                return True
        return False
    
    def get_polyps_by_location(self, location: int) -> List[Polyp]:
        """Get all polyps at a specific location.
        
        Args:
            location: Anatomical location (1-6)
            
        Returns:
            List[Polyp]: List of polyps at the specified location
        """
        return [p for p in self.polyps if p.location == location]
    
    def get_advanced_polyps(self) -> List[Polyp]:
        """Get all advanced polyps (stage > 3 or size > 1cm).
        
        Returns:
            List[Polyp]: List of advanced polyps
        """
        return [p for p in self.polyps if p.stage > 3 or p.size > 1.0]
    
    def has_metastatic_cancer(self) -> bool:
        """Check if patient has metastatic cancer.
        
        Returns:
            bool: True if patient has stage 4 cancer
        """
        return any(cancer.stage >= 4 for cancer in self.cancers)
    
    def get_cancer_stages(self) -> List[int]:
        """Get stages of all cancers.
        
        Returns:
            List[int]: List of cancer stages
        """
        return [cancer.stage for cancer in self.cancers]
    
    def get_cancer_locations(self) -> List[int]:
        """Get locations of all cancers.
        
        Returns:
            List[int]: List of cancer locations
        """
        return [cancer.location for cancer in self.cancers]
    
    def set_natural_death_year(self, year: int) -> None:
        """Set the year when patient would die from natural causes.
        
        Args:
            year: Year of natural death
        """
        self.natural_death_year = year
    
    def is_alive(self, current_year: int) -> bool:
        """Check if patient is alive at the given year.
        
        Args:
            current_year: The year to check
            
        Returns:
            bool: True if patient is alive, False otherwise
        """
        return self.death_year is None or current_year < self.death_year
    
    def get_age_at_year(self, year: int) -> int:
        """Calculate patient's age at a specific year.
        
        Args:
            year: The year to calculate age for
            
        Returns:
            int: Patient's age at the specified year
        """
        return year - self.birth_year
    
    def get_polyp_count_by_year(self) -> Dict[int, int]:
        """Get polyp count history by year.
        
        Returns:
            Dict[int, int]: Dictionary mapping years to polyp counts
        """
        # This would require tracking polyp history over time
        # Simplified implementation:
        result = {}
        for screening in self.screening_history:
            year = screening['year']
            if 'polyp_count' in screening['findings']:
                result[year] = screening['findings']['polyp_count']
        return result
    
    def get_risk_score(self) -> float:
        """Calculate overall risk score based on risk factors and history.
        
        Returns:
            float: Risk score where higher values indicate higher risk
        """
        base_score = self.individual_risk
        
        # Adjust for polyp history
        if len(self.polyps) > 0 or any(s['findings'].get('polyp_count', 0) > 0 for s in self.screening_history):
            base_score *= 1.5
        
        # Adjust for advanced polyps
        if len(self.get_advanced_polyps()) > 0:
            base_score *= 2.0
        
        # Adjust for cancer history
        if len(self.cancers) > 0:
            base_score *= 3.0
        
        return base_score
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert patient to dictionary for serialization.
        
        Returns:
            Dict[str, any]: Dictionary representation of patient
        """
        return {
            'id': self.id,
            'age': self.age,
            'gender': self.gender,
            'risk_factors': self.risk_factors,
            'individual_risk': self.individual_risk,
            'polyps': [p.to_dict() for p in self.polyps],
            'cancers': [c.to_dict() for c in self.cancers],
            'death_year': self.death_year,
            'death_cause': self.death_cause,
            'screening_history': self.screening_history,
            'treatment_history': self.treatment_history,
            'birth_year': self.birth_year,
            'natural_death_year': self.natural_death_year
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Patient':
        """Create patient from dictionary.
        
        Args:
            data: Dictionary containing patient data
            
        Returns:
            Patient: New patient instance
        """
        patient = cls(
            id=data['id'],
            age=data['age'],
            gender=data['gender'],
            risk_factors=data['risk_factors'],
            death_year=data.get('death_year'),
            death_cause=data.get('death_cause')
        )
        
        # Restore derived attributes
        patient.birth_year = data.get('birth_year', datetime.now().year - patient.age)
        patient.natural_death_year = data.get('natural_death_year')
        patient.individual_risk = data.get('individual_risk', patient.individual_risk)
        
        # Restore polyps
        patient.polyps = [Polyp.from_dict(p) for p in data.get('polyps', [])]
        
        # Restore cancers
        patient.cancers = [Cancer.from_dict(c) for c in data.get('cancers', [])]
        
        # Restore history
        patient.screening_history = data.get('screening_history', [])
        patient.treatment_history = data.get('treatment_history', [])
        
        return patient

