"""
Polyp model for CMOST simulation.
"""
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import numpy as np
from datetime import datetime


@dataclass
class Polyp:
    """Represents a colorectal polyp in the simulation."""
    
    id: int
    location: int  # Anatomical location (1-6 representing different colon segments)
    size: float    # Size in cm
    stage: int     # Stage (1-6, corresponding to PolypStage enum)
    patient_id: int
    patient_gender: str
    
    # Tracking information
    discovery_year: Optional[int] = None
    discovery_method: Optional[str] = None
    year_created: int = field(default_factory=lambda: datetime.now().year)
    
    # Additional properties
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def progress(self, years: int, progression_model, individual_risk: float, age: Optional[int] = None) -> bool:
        """Progress the polyp by a number of years.
        
        Args:
            years: Number of years to progress
            progression_model: Model that defines progression rates
            individual_risk: <PERSON><PERSON>'s individual risk multiplier
            age: <PERSON><PERSON>'s current age (optional)
            
        Returns:
            bool: True if polyp progressed to cancer, False otherwise
        """
        progressed_to_cancer = False
        
        for _ in range(years):
            new_stage, is_cancer = progression_model.progress_polyp(
                self.stage, 
                self.location, 
                self.patient_gender, 
                individual_risk,
                age
            )
            
            # Update size based on stage change
            if new_stage > self.stage:
                self.size = self._calculate_new_size(new_stage)
            elif new_stage < self.stage and new_stage > 0:
                # Regression
                self.size = self.size * 0.8  # Shrink by 20%
            elif new_stage == 0:
                # Polyp disappeared
                self.size = 0
                
            self.stage = new_stage
            
            if is_cancer:
                progressed_to_cancer = True
                break
                
            # If polyp disappeared, stop progression
            if self.stage == 0:
                break
                
        return progressed_to_cancer
    
    def _calculate_new_size(self, new_stage: int) -> float:
        """Calculate new polyp size based on stage progression.
        
        Args:
            new_stage: New polyp stage
            
        Returns:
            float: New size in cm
        """
        # Size ranges by stage (in cm)
        size_ranges = {
            1: (0.1, 0.5),    # Diminutive: <5mm
            2: (0.5, 0.9),    # Small: 5-9mm
            3: (1.0, 1.9),    # Medium: 10-19mm
            4: (2.0, 5.0),    # Large: ≥20mm
            5: (1.0, 5.0),    # Advanced with villous features (size varies)
            6: (1.0, 5.0)     # Advanced with high-grade dysplasia (size varies)
        }
        
        if new_stage in size_ranges:
            min_size, max_size = size_ranges[new_stage]
            
            # If current size is already in range, increase slightly
            if min_size <= self.size <= max_size:
                return min(self.size * 1.2, max_size)
            else:
                # Otherwise, pick a size in the appropriate range
                return min_size + np.random.random() * (max_size - min_size)
        
        return self.size  # Default: no change
    
    @property
    def is_advanced(self) -> bool:
        """Check if polyp is advanced.
        
        Returns:
            bool: True if polyp is advanced (stage 5-6), False otherwise
        """
        return self.stage >= 5
    
    @property
    def is_diminutive(self) -> bool:
        """Check if polyp is diminutive.
        
        Returns:
            bool: True if polyp is diminutive (stage 1), False otherwise
        """
        return self.stage == 1
    
    def estimate_dwell_time(self, progression_model, individual_risk: float) -> float:
        """Estimate expected dwell time from current stage to cancer.
        
        Args:
            progression_model: Model that defines progression rates
            individual_risk: Patient's individual risk multiplier
            
        Returns:
            float: Expected dwell time in years
        """
        return progression_model.estimate_dwell_time(
            self.stage,
            self.location,
            self.patient_gender,
            individual_risk
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert polyp to dictionary for serialization.

        Returns:
            Dict[str, Any]: Dictionary representation of polyp
        """
        return {
            'id': self.id,
            'location': self.location,
            'size': self.size,
            'stage': self.stage,
            'patient_id': self.patient_id,
            'patient_gender': self.patient_gender,
            'discovery_year': self.discovery_year,
            'discovery_method': self.discovery_method,
            'year_created': self.year_created,
            'properties': self.properties
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Polyp':
        """Create polyp from dictionary.

        Args:
            data: Dictionary containing polyp data

        Returns:
            Polyp: New polyp instance
        """
        return cls(
            id=data['id'],
            location=data['location'],
            size=data['size'],
            stage=data['stage'],
            patient_id=data['patient_id'],
            patient_gender=data['patient_gender'],
            discovery_year=data.get('discovery_year'),
            discovery_method=data.get('discovery_method'),
            year_created=data.get('year_created', datetime.now().year),
            properties=data.get('properties', {})
        )