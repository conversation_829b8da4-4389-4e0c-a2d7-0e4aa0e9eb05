"""
Core simulation engine for CMOST.
"""
from typing import Dict, List, Optional, Tuple, Any, Callable
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging
from datetime import datetime

from ..models.patient import Patient
from ..models.polyp import Polyp
from ..models.cancer import Cancer
from ..config.settings import Settings
from ..utils.statistics import calculate_statistics
from ..screening.strategy_manager import ScreeningStrategyManager
from ..screening.dynamic_scheduler import DynamicScreeningScheduler


class Simulation:
    """Main simulation engine for colorectal cancer progression."""
    
    def __init__(self, settings: Settings):
        """Initialize simulation with settings."""
        self.settings = settings
        self.patients: List[Patient] = []
        self.results: Dict[str, Any] = {}
        self.current_year = 0
        self.start_time = None
        self.end_time = None
        self.progression_model = self._load_progression_model()
        self.screening_manager = ScreeningStrategyManager()
        self.dynamic_scheduler = DynamicScreeningScheduler()
        self.logger = self._setup_logger()

    def _get_setting(self, key: str, default=None):
        """Helper method to get settings with dot notation."""
        return self.settings.get(key, default)
        
    def _setup_logger(self):
        """Set up logging for the simulation."""
        logger = logging.getLogger("CMOST_Simulation")
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
        
    def _load_progression_model(self):
        """Load the disease progression model based on settings."""
        from .progression import ProgressionModel
        return ProgressionModel(
            dwell_speed=self._get_setting('ModelParameters.dwell_speed', 1.0),
            location_factors=self._get_setting('ModelParameters.location_progression_factors', {}),
            gender_factors=self._get_setting('ModelParameters.gender_progression_factors', {})
        )
    
    def initialize_population(self) -> None:
        """Create initial patient population."""
        num_patients = self._get_setting('Number_patients', 10000)
        self.logger.info(f"Initializing population with {num_patients} patients")
        
        # Create patients with age and gender distribution
        for i in range(num_patients):
            gender = 'M' if np.random.random() < self._get_setting('ModelParameters.male_proportion', 0.5) else 'F'
            age = self._sample_age_distribution(gender)
            
            patient = Patient(
                id=i,
                age=age,
                gender=gender,
                risk_factors=self._assign_risk_factors()
            )
            
            # Initialize with polyps based on prevalence
            self._initialize_polyps(patient)
            
            # Set natural death year based on life tables
            self._set_natural_death_year(patient)
            
            self.patients.append(patient)
    
    def _sample_age_distribution(self, gender: str) -> int:
        """Sample from age distribution based on gender."""
        if hasattr(self.settings, 'age_distribution') and self.settings.age_distribution:
            # Use configured age distribution if available
            age_dist = self.settings.age_distribution.get(gender, self.settings.age_distribution.get('default', None))
            if age_dist:
                return int(age_dist.sample())
        
        # Default implementation with normal distribution
        mean_age = 50
        std_dev = 15
        return max(18, min(100, int(np.random.normal(mean_age, std_dev))))
    
    def _assign_risk_factors(self) -> Dict[str, float]:
        """Assign risk factors to a patient."""
        risk_factors = {}
        for factor, distribution in self.settings.risk_factor_distributions.items():
            risk_factors[factor] = distribution.sample()
        return risk_factors
    
    def _initialize_polyps(self, patient: Patient) -> None:
        """Initialize patient with polyps based on prevalence."""
        # Determine number of initial polyps based on age, gender, and risk
        age_key = min(patient.age // 10 * 10, max(self.settings.polyp_prevalence_by_age.keys()))
        base_prevalence = self.settings.polyp_prevalence_by_age[age_key]
        adjusted_prevalence = base_prevalence * patient.individual_risk
        
        num_polyps = np.random.poisson(adjusted_prevalence)
        
        for _ in range(num_polyps):
            location = self._sample_polyp_location()
            patient.add_polyp(location)
    
    def _sample_polyp_location(self) -> int:
        """Sample polyp location based on anatomical distribution."""
        return np.random.choice(
            range(1, 7),  # 6 colon segments
            p=self.settings.polyp_location_distribution
        )
    
    def _set_natural_death_year(self, patient: Patient) -> None:
        """Set the year when patient would die from natural causes."""
        current_age = patient.age
        mortality_table = self.settings.mortality_tables[patient.gender]
        
        # Determine death year using life table
        death_age = current_age
        while True:
            if death_age >= len(mortality_table) - 1:
                death_age = len(mortality_table) - 1
                break
                
            mortality_rate = mortality_table[death_age]
            if np.random.random() < mortality_rate:
                break
                
            death_age += 1
        
        patient.set_natural_death_year(self.current_year + (death_age - current_age))
    
    def run(self, years: int, callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Run simulation for specified number of years.
        
        Args:
            years: Number of years to simulate
            callback: Optional callback function called every 5 years with current state
            
        Returns:
            Dict containing simulation results
        """
        self.start_time = datetime.now()
        self.logger.info(f"Starting simulation for {years} years")
        
        if not self.patients:
            self.initialize_population()
        
        for year in tqdm(range(years), desc="Simulating years"):
            self._simulate_year()
            
            if callback and year % 5 == 0:
                callback(self.current_year, self.patients)
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        self.logger.info(f"Simulation completed in {duration:.2f} seconds")
        
        self.results = calculate_statistics(self.patients, self.settings)
        return self.results
    
    def _simulate_year(self) -> None:
        """Simulate one year for all patients."""
        self.current_year += 1
        
        # Apply screening if scheduled for this year
        if self._is_screening_year():
            self._perform_screening()
        
        for patient in self.patients:
            if not patient.is_alive(self.current_year):
                continue  # Skip dead patients
                
            # Check for natural death
            if self._check_natural_death(patient):
                patient.death_year = self.current_year
                patient.death_cause = "natural"
                continue
                
            # Generate new polyps
            self._generate_new_polyps(patient)
            
            # Progress existing conditions
            patient.progress(1, self.progression_model)
            
            # Check for cancer death
            if self._check_cancer_death(patient):
                patient.death_year = self.current_year
                patient.death_cause = "cancer"
    
    def _is_screening_year(self) -> bool:
        """Check if current year is a screening year based on settings."""
        if not hasattr(self.settings, 'screening') or not self.settings.screening:
            return False
            
        screening = self.settings.screening
        if not screening.get('enabled', False):
            return False
            
        start_year = screening.get('start_year', 0)
        interval = screening.get('interval', 1)
        
        return self.current_year >= start_year and (self.current_year - start_year) % interval == 0
    
    def _perform_screening(self) -> None:
        """Perform screening on eligible patients using strategy manager."""
        if not hasattr(self.settings, 'screening') or not self.settings.screening:
            return

        screening = self.settings.screening
        strategy_name = screening.get('strategy', 'colonoscopy_only')
        participation_rate = screening.get('participation_rate', 1.0)

        self.logger.info(f"Performing {strategy_name} screening in year {self.current_year}")

        for patient in self.patients:
            if not patient.is_alive(self.current_year):
                continue

            # Check if patient participates in screening
            if np.random.random() > participation_rate:
                continue

            # Get last screening information
            last_screening = None
            if patient.screening_history:
                last_screening = patient.screening_history[-1]

            try:
                # Apply screening strategy
                result = self.screening_manager.apply_strategy(
                    strategy_name,
                    patient,
                    self.current_year,
                    last_screening
                )

                # Record screening in patient history
                patient.screening_history.append(result)

                # Process screening results
                self._process_screening_results(patient, result)

            except Exception as e:
                self.logger.error(f"Error applying screening strategy {strategy_name} to patient {patient.id}: {e}")
                # Fallback to simple colonoscopy
                self._perform_colonoscopy(patient)

    def _process_screening_results(self, patient: Patient, result: Dict[str, Any]) -> None:
        """Process screening results and remove detected lesions.

        Args:
            patient: Patient who was screened
            result: Screening results from strategy manager
        """
        if not result.get("eligible", True):
            return

        # Handle single test results
        if "findings" in result and result["findings"]:
            findings = result["findings"]
            self._remove_detected_lesions(patient, findings)

        # Handle sequential strategy results
        elif "primary_test" in result:
            primary_findings = result["primary_test"].get("findings", {})
            if primary_findings:
                self._remove_detected_lesions(patient, primary_findings)

            # Handle follow-up test
            if "follow_up_test" in result:
                followup_findings = result["follow_up_test"].get("findings", {})
                if followup_findings:
                    self._remove_detected_lesions(patient, followup_findings)

        # Handle parallel strategy results
        elif "tests" in result:
            for test_name, test_result in result["tests"].items():
                test_findings = test_result.get("findings", {})
                if test_findings:
                    self._remove_detected_lesions(patient, test_findings)

    def _remove_detected_lesions(self, patient: Patient, findings: Dict[str, Any]) -> None:
        """Remove detected lesions from patient.

        Args:
            patient: Patient with detected lesions
            findings: Detection findings
        """
        # Remove detected polyps
        for polyp_id in findings.get("polyps", []):
            patient.remove_polyp(polyp_id)

        # Remove detected serrated lesions
        for lesion_id in findings.get("serrated_lesions", []):
            patient.remove_serrated_lesion(lesion_id)

        # Apply cancer treatment for detected cancers
        for cancer_id in findings.get("cancers", []):
            for cancer in patient.cancers:
                if cancer.id == cancer_id:
                    self._apply_cancer_treatment(patient, cancer)
                    break

    def _perform_colonoscopy(self, patient: Patient) -> None:
        """Perform colonoscopy screening on a patient."""
        sensitivity = self.settings.screening.get('colonoscopy_sensitivity', 0.95)
        
        findings = {
            'polyp_count': 0,
            'serrated_lesion_count': 0,
            'cancer_count': 0,
            'polyps_removed': [],
            'serrated_lesions_removed': [],
            'cancers_detected': []
        }

        # Check for polyps
        for polyp in list(patient.polyps):  # Use list to allow removal during iteration
            if np.random.random() < sensitivity:
                # Polyp detected
                findings['polyp_count'] += 1
                findings['polyps_removed'].append(polyp.id)

                # Remove polyp
                patient.remove_polyp(polyp.id)

        # Check for serrated lesions (harder to detect than adenomas)
        for lesion in list(patient.serrated_lesions):
            lesion_sensitivity = lesion.get_screening_detectability('colonoscopy')
            if np.random.random() < lesion_sensitivity:
                # Serrated lesion detected
                findings['serrated_lesion_count'] += 1
                findings['serrated_lesions_removed'].append(lesion.id)

                # Remove serrated lesion
                patient.remove_serrated_lesion(lesion.id)

        # Check for cancers
        for cancer in patient.cancers:
            if np.random.random() < sensitivity:
                # Cancer detected
                findings['cancer_count'] += 1
                findings['cancers_detected'].append(cancer.id)

                # Apply treatment based on cancer stage
                self._apply_cancer_treatment(patient, cancer)
        
        # Record screening
        patient.add_screening(self.current_year, 'colonoscopy', findings)
    
    def _perform_sigmoidoscopy(self, patient: Patient) -> None:
        """Perform sigmoidoscopy screening on a patient."""
        sensitivity = self.settings.screening.get('sigmoidoscopy_sensitivity', 0.90)
        # Sigmoidoscopy only examines distal colon (locations 1-3)
        distal_locations = [1, 2, 3]
        
        findings = {
            'polyp_count': 0,
            'serrated_lesion_count': 0,
            'cancer_count': 0,
            'polyps_removed': [],
            'serrated_lesions_removed': [],
            'cancers_detected': []
        }

        # Check for polyps in distal colon
        for polyp in list(patient.polyps):
            if polyp.location in distal_locations and np.random.random() < sensitivity:
                # Polyp detected
                findings['polyp_count'] += 1
                findings['polyps_removed'].append(polyp.id)

                # Remove polyp
                patient.remove_polyp(polyp.id)

        # Check for serrated lesions in distal colon
        for lesion in list(patient.serrated_lesions):
            if lesion.location in distal_locations:
                lesion_sensitivity = lesion.get_screening_detectability('sigmoidoscopy')
                if np.random.random() < lesion_sensitivity:
                    # Serrated lesion detected
                    findings['serrated_lesion_count'] += 1
                    findings['serrated_lesions_removed'].append(lesion.id)

                    # Remove serrated lesion
                    patient.remove_serrated_lesion(lesion.id)

        # Check for cancers in distal colon
        for cancer in patient.cancers:
            if cancer.location in distal_locations and np.random.random() < sensitivity:
                # Cancer detected
                findings['cancer_count'] += 1
                findings['cancers_detected'].append(cancer.id)

                # Apply treatment
                self._apply_cancer_treatment(patient, cancer)
        
        # Record screening
        patient.add_screening(self.current_year, 'sigmoidoscopy', findings)
    
    def _perform_fobt(self, patient: Patient) -> None:
        """Perform fecal occult blood test (FOBT) screening on a patient."""
        sensitivity_polyp = self.settings.screening.get('fobt_sensitivity_polyp', 0.10)
        sensitivity_cancer = self.settings.screening.get('fobt_sensitivity_cancer', 0.70)
        
        findings = {
            'result': 'negative',
            'follow_up': None
        }
        
        # Check for advanced polyps
        has_advanced_polyps = any(p.size > 1.0 for p in patient.polyps)
        if has_advanced_polyps and np.random.random() < sensitivity_polyp:
            findings['result'] = 'positive'

        # Check for advanced serrated lesions (lower sensitivity than adenomas)
        has_advanced_serrated = any(l.is_advanced for l in patient.serrated_lesions)
        if has_advanced_serrated:
            # Use lesion-specific detectability for FIT
            for lesion in patient.serrated_lesions:
                if lesion.is_advanced:
                    lesion_sensitivity = lesion.get_screening_detectability('fit')
                    if np.random.random() < lesion_sensitivity:
                        findings['result'] = 'positive'
                        break

        # Check for cancer
        if patient.cancers and np.random.random() < sensitivity_cancer:
            findings['result'] = 'positive'
        
        # If positive, schedule follow-up colonoscopy
        if findings['result'] == 'positive':
            findings['follow_up'] = 'colonoscopy'
            # Perform follow-up colonoscopy
            self._perform_colonoscopy(patient)
        
        # Record screening
        patient.add_screening(self.current_year, 'fobt', findings)
    
    def _apply_cancer_treatment(self, patient: Patient, cancer: Cancer) -> None:
        """Apply treatment to a detected cancer."""
        treatment_type = 'surgery'
        if cancer.stage >= 3:
            treatment_type = 'surgery+chemo'
        
        # Record treatment
        treatment_details = {
            'cancer_id': cancer.id,
            'cancer_stage': cancer.stage,
            'cancer_location': cancer.location
        }
        
        patient.add_treatment(self.current_year, treatment_type, treatment_details)
        
        # Apply treatment effect (simplified model)
        # In a more detailed model, this would modify cancer progression
        if cancer.stage < 4:
            # For non-metastatic cancer, treatment has high success rate
            cancer.treatment_applied = True
    
    def _check_natural_death(self, patient: Patient) -> bool:
        """Check if patient dies from natural causes this year."""
        if patient.natural_death_year is not None and self.current_year >= patient.natural_death_year:
            return True
            
        # Fallback to annual probability if natural_death_year not set
        mortality_rate = self.settings.mortality_tables[patient.gender][min(patient.age, len(self.settings.mortality_tables[patient.gender])-1)]
        return np.random.random() < mortality_rate
    
    def _generate_new_polyps(self, patient: Patient) -> None:
        """Generate new polyps for a patient in the current year."""
        # Base incidence rate adjusted for age, gender and risk factors
        age_key = min(patient.age // 10 * 10, max(self.settings.polyp_incidence_by_age.keys()))
        base_rate = self.settings.polyp_incidence_by_age[age_key]
        adjusted_rate = base_rate * patient.individual_risk
        
        # Poisson distribution for number of new polyps
        num_new_polyps = np.random.poisson(adjusted_rate)
        
        for _ in range(num_new_polyps):
            location = self._sample_polyp_location()
            patient.add_polyp(location)
    
    def _check_cancer_death(self, patient: Patient) -> bool:
        """Check if patient dies from cancer this year."""
        for cancer in patient.cancers:
            if cancer.stage >= 4:  # Metastatic cancer
                # Treatment can reduce mortality
                mortality_modifier = 0.7 if cancer.treatment_applied else 1.0
                mortality_rate = self.settings.cancer_mortality_by_stage[cancer.stage] * mortality_modifier
                
                if np.random.random() < mortality_rate:
                    return True
        return False
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get summary statistics from the simulation.
        
        Returns:
            Dict containing summary statistics
        """
        if not self.results:
            self.results = calculate_statistics(self.patients, self.settings)
            
        return {
            'total_patients': len(self.patients),
            'years_simulated': self.current_year,
            'cancer_incidence': self.results.get('cancer_incidence', 0),
            'cancer_mortality': self.results.get('cancer_mortality', 0),
            'polyps_detected': self.results.get('polyps_detected', 0),
            'advanced_polyps': self.results.get('advanced_polyps', 0),
            'screening_tests': self.results.get('screening_tests', 0),
            'life_years_gained': self.results.get('life_years_gained', 0),
            'simulation_duration': (self.end_time - self.start_time).total_seconds() if self.end_time else None
        }
    
    def get_patient_dataframe(self) -> pd.DataFrame:
        """Convert patient data to pandas DataFrame for analysis.
        
        Returns:
            DataFrame containing patient data
        """
        data = []
        for patient in self.patients:
            patient_data = {
                'id': patient.id,
                'gender': patient.gender,
                'initial_age': patient.age - self.current_year,
                'final_age': patient.age,
                'risk_score': patient.get_risk_score(),
                'death_year': patient.death_year,
                'death_cause': patient.death_cause,
                'polyp_count': len(patient.polyps),
                'cancer_count': len(patient.cancers),
                'screening_count': len(patient.screening_history),
                'treatment_count': len(patient.treatment_history)
            }
            data.append(patient_data)
            
        return pd.DataFrame(data)
    
    def save_results(self, filename: str) -> None:
        """Save simulation results to file.
        
        Args:
            filename: Path to save results
        """
        import json
        
        # Get summary statistics
        summary = self.get_summary_statistics()
        
        # Prepare patient data (simplified for storage)
        patient_data = []
        for patient in self.patients:
            patient_data.append(patient.to_dict())
        
        # Combine data
        output_data = {
            'summary': summary,
            'settings': self.settings.to_dict() if hasattr(self.settings, 'to_dict') else vars(self.settings),
            'patients': patient_data
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(output_data, f, indent=2)
            
        self.logger.info(f"Results saved to {filename}")
    
    @classmethod
    def load_results(cls, filename: str) -> 'Simulation':
        """Load simulation results from file.
        
        Args:
            filename: Path to load results from
            
        Returns:
            Simulation object with loaded data
        """
        import json
        from ..config.settings import Settings
        
        with open(filename, 'r') as f:
            data = json.load(f)
        
        # Create settings
        settings = Settings()
        if 'settings' in data:
            settings.update(data['settings'])
        
        # Create simulation
        simulation = cls(settings)
        
        # Load patients
        for patient_data in data['patients']:
            patient = Patient.from_dict(patient_data)
            simulation.patients.append(patient)
        
        # Set current year based on patients
        max_age = max(patient.age for patient in simulation.patients)
        min_initial_age = min(patient.age - (patient.death_year or 0) for patient in simulation.patients)
        simulation.current_year = max_age - min_initial_age
        
        return simulation

    def import_patients(self, patients: List[Patient]) -> None:
        """Import existing patients into the simulation.

        Args:
            patients: List of patients to import
        """
        self.logger.info(f"Importing {len(patients)} patients into natural population simulation")
        self.patients.extend(patients)

        # Update current year based on imported patients if needed
        if patients and self.current_year == 0:
            # Estimate current year based on patient ages
            avg_age = sum(p.age for p in patients) / len(patients)
            self.current_year = int(avg_age / 2)  # Rough estimate
