["tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_summary", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_with_multiple_targets", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_with_single_target", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibrator_initialization", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_parameter_bounds_update", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_parameter_effect_simulation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_creation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_validation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_with_features", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_error_metrics", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_residuals_calculation", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_parameter_space", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_sampling", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_value_clipping", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_parameter_space_creation", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_random_sampling", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_value_clipping", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_optimization_simple_function", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_optimizer_initialization", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_random_parameter_sampling", "tests/unit/test_dual_architecture.py::TestArchitectureConfig::test_custom_config", "tests/unit/test_dual_architecture.py::TestArchitectureConfig::test_default_config", "tests/unit/test_dual_architecture.py::TestDualArchitectureIntegration::test_hybrid_mode_configuration", "tests/unit/test_dual_architecture.py::TestDualArchitectureIntegration::test_mode_switching_configuration", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_combine_simulation_results", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_get_current_mode", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_get_simulation_summary", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_birth_cohort", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_hybrid", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_natural_population", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_manual_mode_switch", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_single_mode_simulation_birth_cohort", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_single_mode_simulation_natural", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_switch_to_same_mode", "tests/unit/test_dual_architecture.py::TestSimulationModeEnum::test_enum_comparison", "tests/unit/test_dual_architecture.py::TestSimulationModeEnum::test_enum_values", "tests/unit/test_dynamic_scheduling.py::TestDynamicSchedulingRules::test_custom_rules", "tests/unit/test_dynamic_scheduling.py::TestDynamicSchedulingRules::test_default_rules", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_get_recommended_test_first_time", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_get_recommended_test_with_history", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_no_findings", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_poor_quality", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_with_findings", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_is_screening_due_first_time", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_is_screening_due_with_history", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_caching", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_high_risk", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_low_risk", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_lynch_syndrome", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_schedule_next_screening", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_schedule_next_screening_elderly", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_screening_summary", "tests/unit/test_dynamic_scheduling.py::TestScreeningEvent::test_screening_event_creation", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_age_specific_risk_fap", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_age_specific_risk_lynch", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_default_genetic_profile", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_fap_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_lynch_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_map_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_polyp_risk_modifier", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_screening_recommendations", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_default_molecular_profile", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_molecular_interactions", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_progression_modifier", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_serialization", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_serrated_pathway_classification", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_traditional_pathway_classification", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_treatment_response_modifier", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_adenoma_profile_generation", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_age_effect_on_mutations", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_serrated_profile_generation", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_genetic_risk_calculation", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_patient_genetic_profile_initialization", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_polyp_molecular_profile_integration", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_serrated_lesion_molecular_profile_integration", "tests/unit/test_health_economics.py::TestCostData::test_cost_data_creation", "tests/unit/test_health_economics.py::TestCostData::test_cost_data_with_components", "tests/unit/test_health_economics.py::TestCostData::test_cost_discounting", "tests/unit/test_health_economics.py::TestCostData::test_cost_no_discounting_base_year", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_cost", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_qaly", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_qaly_custom_utility", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_calculate_patient_outcomes", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_calculate_population_outcomes", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_compare_strategies", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_compare_strategies_dominance", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_cost_breakdown_specific_patients", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_evaluator_initialization", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_get_cost_breakdown", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_standard_cost_values", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_standard_utility_values", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_calculation", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_creation", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_discounting", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_parallel_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_sequential_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_single_test_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_add_custom_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_age_eligibility", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_detection_range_limitations", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_get_predefined_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_lesion_detection", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_manager_initialization", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_parallel_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_polyp_classification", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_risk_stratified_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_sequential_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_single_test_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_unknown_strategy_error", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_colonoscopy_characteristics", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_fit_characteristics", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_sigmoidoscopy_characteristics", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_add_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_advanced_serrated_lesions", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_serrated_lesions_by_location", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_remove_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_cancer_risk_level", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_advanced_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_large_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_lesion_creation", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_screening_detectability", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_cancer_conversion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_dict_and_from_dict", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_lesion_progression_integration", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_progression_model_method"]