["tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_add_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_advanced_serrated_lesions", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_serrated_lesions_by_location", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_remove_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_cancer_risk_level", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_advanced_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_large_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_lesion_creation", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_screening_detectability", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_cancer_conversion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_dict_and_from_dict", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_lesion_progression_integration", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_progression_model_method"]